import type { APIRoute } from 'astro';
import { createCloudflareAPI, isValidDomain } from '../../lib/cloudflare-api';
import { nanoid } from 'nanoid';

export const prerender = false;

// Generate verification token for domain ownership
function generateVerificationToken(): string {
  return `cf-domain-verification-${nanoid(32)}`;
}

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const searchParams = url.searchParams;
    const userId = searchParams.get("user_id");

    if (!userId) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "User ID is required" 
      }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch domains for the user
    const domainsResult = await db.prepare(`
      SELECT
        id,
        domain,
        status,
        ssl_status,
        verification_method,
        verification_token,
        cloudflare_zone_id,
        cloudflare_custom_hostname_id,
        pages_project_name,
        verified,
        last_verified_at,
        error_message,
        created_at,
        updated_at
      FROM custom_domains
      WHERE user_id = ?
      ORDER BY created_at DESC
    `).bind(userId).all();

    const domains = domainsResult.results.map((domain: any) => ({
      ...domain,
      verified: domain.verified === 1,
      dns_records: {
        cname: {
          type: 'CNAME',
          name: "qr",
          value: "qr.qranalytica.com"
        }
      }
    }));

    return new Response(JSON.stringify({
      success: true,
      domains
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error fetching domains:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to fetch domains'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { domain,  user_id } = body as { domain: string;  user_id: string };
    const pages_project_name = "qr-redirect-backend-v2";
    // Validate input
    if (!domain || !user_id) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain and user_id are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Validate domain format
    if (!isValidDomain(domain)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid domain format'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if domain already exists
    const existingDomain = await db.prepare(`
      SELECT id FROM custom_domains WHERE domain = ?
    `).bind(domain).first();

    if (existingDomain) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain already exists'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Ensure user exists (create if not exists)
    const existingUser = await db.prepare(`
      SELECT id FROM users WHERE id = ?
    `).bind(user_id).first();

    if (!existingUser) {
      await db.prepare(`
        INSERT INTO users (id, email, name, created_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `).bind(user_id, `${user_id}@example.com`, user_id).run();
    }

    // Generate verification token
    const verificationToken = generateVerificationToken();
    const domainId = nanoid();

    // Try to add domain to Cloudflare Pages if credentials are available
    let cloudflareError = null;
    let cloudflareSuccess = false;

    try {
      if (env.CLOUDFLARE_API_TOKEN && env.CLOUDFLARE_ACCOUNT_ID && pages_project_name) {
        const cloudflareAPI = createCloudflareAPI(env);
        console.log("🚀 ~ POST ~ cloudflareAPI:", cloudflareAPI)
        
        // Add domain to Cloudflare Pages project
        const result = await cloudflareAPI.addPagesDomain(pages_project_name, domain);
        console.log("🚀 ~ POST ~ result:", result)

        if ((result as any).success) {
          cloudflareSuccess = true;
          console.log('Domain added to Cloudflare Pages successfully:', result);
        } else {
          cloudflareError = `Cloudflare API error: ${JSON.stringify((result as any).errors || result)}`;
        }
      }
    } catch (error) {
      console.error('Cloudflare API error:', error);
      cloudflareError = `Failed to add domain to Cloudflare Pages: ${(error as Error).message}`;
    }

    // Insert domain into database
    const insertResult = await db.prepare(`
      INSERT INTO custom_domains (
        id,
        user_id,
        domain,
        status,
        ssl_status,
        verification_method,
        verification_token,
        pages_project_name,
        verified,
        error_message,
        created_at,
        updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `).bind(
      domainId,
      user_id,
      domain,
      cloudflareSuccess ? 'pending' : 'failed',
      'pending',
      'dns',
      verificationToken,
      pages_project_name || null,
      0,
      cloudflareError
    ).run();

    if (!insertResult.success) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to save domain to database'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Fetch the created domain
    const createdDomain = await db.prepare(`
      SELECT
        id,
        domain,
        status,
        ssl_status,
        verification_method,
        verification_token,
        cloudflare_zone_id,
        cloudflare_custom_hostname_id,
        pages_project_name,
        verified,
        last_verified_at,
        error_message,
        created_at,
        updated_at
      FROM custom_domains
      WHERE id = ?
    `).bind(domainId).first();

    const domainResponse = {
      ...createdDomain,
      verified: (createdDomain as any)?.verified === 1,
      dns_records: {
        cname: {
          type: 'CNAME',
          name: "qr",
          value: "qr.qranalytica.com"
        }
      }
    };

    return new Response(JSON.stringify({
      success: true,
      domain: domainResponse,
      message: cloudflareSuccess
        ? 'Domain added successfully to Cloudflare Pages'
        : 'Domain saved locally. Cloudflare integration failed but you can set it up manually.',
      cloudflare_error: cloudflareError
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error adding domain:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to add domain'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

export const DELETE: APIRoute = async ({ url, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: "Database not configured." 
      }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const searchParams = url.searchParams;
    const domainId = searchParams.get("id");
    const userId = searchParams.get("user_id");

    if (!domainId || !userId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain ID and user_id are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domain details before deletion
    const domain = await db.prepare(`
      SELECT domain, pages_project_name FROM custom_domains
      WHERE id = ? AND user_id = ?
    `).bind(domainId, userId).first();

    if (!domain) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Domain not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Try to remove domain from Cloudflare Pages if credentials are available
    try {
      if (env.CLOUDFLARE_API_TOKEN && env.CLOUDFLARE_ACCOUNT_ID && (domain as any).pages_project_name) {
        const cloudflareAPI = createCloudflareAPI(env);
        await cloudflareAPI.removePagesDomain((domain as any).pages_project_name, (domain as any).domain);
      }
    } catch (error) {
      console.error('Failed to remove domain from Cloudflare Pages:', error);
      // Continue with database deletion even if Cloudflare removal fails
    }

    // Delete domain from database
    const deleteResult = await db.prepare(`
      DELETE FROM custom_domains WHERE id = ? AND user_id = ?
    `).bind(domainId, userId).run();

    if (!deleteResult.success) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to delete domain'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Domain deleted successfully'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error deleting domain:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to delete domain'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
