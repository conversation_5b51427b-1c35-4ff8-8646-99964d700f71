// Test the default DNS records configuration
const BASE_URL = 'http://localhost:4322';

async function testDefaultDNS() {
  console.log('🧪 Testing Default DNS Records Configuration...\n');

  try {
    // Test adding a domain to see the default DNS records
    const response = await fetch(`${BASE_URL}/api/custom-domains`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        domain: 'test-default-dns.example.com',
        user_id: 'default-user'
      }),
    });

    const data = await response.json();
    
    if (data.success && data.domain.dns_records) {
      console.log('✅ Domain created successfully with DNS records:');
      console.log('CNAME Record:', JSON.stringify(data.domain.dns_records.cname, null, 2));
      
      if (data.domain.dns_records.txt) {
        console.log('TXT Record:', JSON.stringify(data.domain.dns_records.txt, null, 2));
      }
      
      // Verify the CNAME record has the expected default values
      const { cname } = data.domain.dns_records;
      
      if (cname.name === "qr" && cname.value === "qr.qranalytica.com") {
        console.log('✅ CNAME record has correct default values');
      } else {
        console.log('❌ CNAME record does not have expected default values');
        console.log('Expected: name="qr", value="qr.qranalytica.com"');
        console.log(`Actual: name="${cname.name}", value="${cname.value}"`);
      }
      
      // Clean up - delete the test domain
      const deleteResponse = await fetch(`${BASE_URL}/api/custom-domains?id=${data.domain.id}&user_id=default-user`, {
        method: 'DELETE',
      });
      
      if (deleteResponse.ok) {
        console.log('✅ Test domain cleaned up successfully');
      }
      
    } else {
      console.log('❌ Failed to create domain or DNS records missing');
      console.log('Response:', JSON.stringify(data, null, 2));
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDefaultDNS();
