import React, { useState, useEffect } from 'react';
import { Plus, Globe, CheckCircle, XCircle, Clock, AlertTriangle, Trash2, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON>, ExternalLink, <PERSON>tings, Zap } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Alert, AlertDescription } from '../ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Separator } from '../ui/separator';
import { toast } from 'sonner';

interface CustomDomain {
  id: string;
  domain: string;
  status: 'pending' | 'active' | 'failed' | 'expired';
  ssl_status: 'pending' | 'active' | 'failed';
  verification_method: 'dns' | 'http';
  verification_token?: string;
  dns_records?: {
    cname: { name: string; value: string; type: string };
    txt: { name: string; value: string; type: string };
  };
  cloudflare_zone_id?: string;
  cloudflare_custom_hostname_id?: string;
  pages_project_name?: string;
  verified: boolean;
  last_verified_at?: string;
  error_message?: string;
  created_at: string;
  updated_at: string;
  cloudflare_status?: {
    zone_exists: boolean;
    dns_records: any[];
  };
}

const CustomDomainsManager: React.FC = () => {
  const [domains, setDomains] = useState<CustomDomain[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newDomain, setNewDomain] = useState('');
  const [projectName, setProjectName] = useState('qr-redirect-backend-v2');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchDomains();
  }, []);

  const fetchDomains = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/custom-domains?user_id=default-user');
      const data = await response.json();
      
      if (data.success) {
        setDomains(data.domains);
      } else {
        toast.error('Failed to fetch domains');
      }
    } catch (error) {
      console.error('Error fetching domains:', error);
      toast.error('Failed to fetch domains');
    } finally {
      setLoading(false);
    }
  };

  const addDomain = async () => {
    if (!newDomain.trim()) {
      toast.error('Please enter a domain name');
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch('/api/custom-domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: newDomain.trim(),
          pages_project_name: projectName.trim() || undefined,
          user_id: 'default-user'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Domain added successfully');
        setDomains([data.domain, ...domains]);
        setNewDomain('');
        setProjectName('');
        setIsAddDialogOpen(false);
      } else {
        toast.error(data.error || 'Failed to add domain');
      }
    } catch (error) {
      console.error('Error adding domain:', error);
      toast.error('Failed to add domain');
    } finally {
      setIsSubmitting(false);
    }
  };

  const verifyDomain = async (domainId: string) => {
    try {
      const response = await fetch('/api/custom-domains/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain_id: domainId,
          user_id: 'default-user'
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Domain verified successfully');
        fetchDomains(); // Refresh the list
      } else {
        toast.error(data.error || 'Domain verification failed');
      }
    } catch (error) {
      console.error('Error verifying domain:', error);
      toast.error('Failed to verify domain');
    }
  };

  const deleteDomain = async (domainId: string) => {
    if (!confirm('Are you sure you want to delete this domain?')) {
      return;
    }

    try {
      const response = await fetch(`/api/custom-domains?id=${domainId}&user_id=default-user`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Domain deleted successfully');
        setDomains(domains.filter(d => d.id !== domainId));
      } else {
        toast.error(data.error || 'Failed to delete domain');
      }
    } catch (error) {
      console.error('Error deleting domain:', error);
      toast.error('Failed to delete domain');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const setupCloudflare = async (domainId: string) => {
    try {
      const response = await fetch('/api/custom-domains/cloudflare', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain_id: domainId,
          action: 'setup',
          user_id: 'default-user'
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Domain configured with Cloudflare Pages');
        fetchDomains(); // Refresh the list
      } else {
        toast.error(data.error || 'Failed to configure with Cloudflare');
      }
    } catch (error) {
      console.error('Error setting up Cloudflare:', error);
      toast.error('Failed to configure with Cloudflare');
    }
  };

  const removeFromCloudflare = async (domainId: string) => {
    if (!confirm('Are you sure you want to remove this domain from Cloudflare Pages?')) {
      return;
    }

    try {
      const response = await fetch('/api/custom-domains/cloudflare', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain_id: domainId,
          action: 'remove',
          user_id: 'default-user'
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Domain removed from Cloudflare Pages');
        fetchDomains(); // Refresh the list
      } else {
        toast.error(data.error || 'Failed to remove from Cloudflare');
      }
    } catch (error) {
      console.error('Error removing from Cloudflare:', error);
      toast.error('Failed to remove from Cloudflare');
    }
  };

  const getStatusBadge = (status: string, sslStatus?: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-100 text-red-800 border-red-200"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      case 'expired':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200"><AlertTriangle className="w-3 h-3 mr-1" />Expired</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Custom Domains</h1>
            <p className="text-muted-foreground">Manage custom domains for your Cloudflare Pages projects</p>
          </div>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Custom Domains</h1>
          <p className="text-muted-foreground">Manage custom domains for your Cloudflare Pages projects</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
              <Plus className="w-4 h-4 mr-2" />
              Add Domain
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Add Custom Domain</DialogTitle>
              <DialogDescription>
                Add a custom domain to point to your Cloudflare Pages project.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="domain">Domain Name</Label>
                <Input
                  id="domain"
                  placeholder="example.com"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project">Pages Project Name (Optional)</Label>
                <Input
                  id="project"
                  placeholder="my-project"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={addDomain} disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add Domain'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Domains List */}
      {domains.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Globe className="w-12 h-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No custom domains</h3>
            <p className="text-gray-600 text-center mb-4">
              Get started by adding your first custom domain to your Cloudflare Pages project.
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Domain
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {domains.map((domain) => (
            <Card key={domain.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-blue-600" />
                    <div>
                      <CardTitle className="text-lg">{domain.domain}</CardTitle>
                      <CardDescription>
                        {domain.pages_project_name && `Project: ${domain.pages_project_name}`}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(domain.status)}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => verifyDomain(domain.id)}
                      disabled={domain.status === 'active'}
                    >
                      <RefreshCw className="w-3 h-3 mr-1" />
                      Verify
                    </Button>
                    {domain.status === 'active' && !domain.cloudflare_zone_id && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setupCloudflare(domain.id)}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Zap className="w-3 h-3 mr-1" />
                        Setup CF
                      </Button>
                    )}
                    {domain.cloudflare_zone_id && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFromCloudflare(domain.id)}
                        className="text-orange-600 hover:text-orange-700"
                      >
                        <Settings className="w-3 h-3 mr-1" />
                        Remove CF
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteDomain(domain.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {domain.status === 'pending' && domain.dns_records && (
                  <Alert className="mb-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Please add the following DNS records to verify your domain:
                    </AlertDescription>
                  </Alert>
                )}
                
                {domain.dns_records && domain.status === 'pending' && (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Required DNS Records:</h4>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Type</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Value</TableHead>
                            <TableHead>Action</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>{domain.dns_records.cname.type}</TableCell>
                            <TableCell className="font-mono text-sm">{domain.dns_records.cname.name}</TableCell>
                            <TableCell className="font-mono text-sm">{domain.dns_records.cname.value}</TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(domain.dns_records!.cname.value)}
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>{domain.dns_records.txt.type}</TableCell>
                            <TableCell className="font-mono text-sm">{domain.dns_records.txt.name}</TableCell>
                            <TableCell className="font-mono text-sm truncate max-w-xs">{domain.dns_records.txt.value}</TableCell>
                            <TableCell>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(domain.dns_records!.txt.value)}
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                )}

                {domain.cloudflare_zone_id && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <Zap className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Cloudflare Integration</span>
                    </div>
                    <div className="text-xs text-blue-700">
                      <p>✓ Domain configured with Cloudflare Pages</p>
                      <p>✓ DNS records managed automatically</p>
                      <p>✓ SSL certificate provisioned</p>
                    </div>
                  </div>
                )}

                {domain.error_message && (
                  <Alert className="mt-4 border-red-200 bg-red-50">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {domain.error_message}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex items-center justify-between mt-4 pt-4 border-t">
                  <div className="text-sm text-gray-600">
                    Added {new Date(domain.created_at).toLocaleDateString()}
                    {domain.last_verified_at && (
                      <span className="ml-2">
                        • Last verified {new Date(domain.last_verified_at).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                  {domain.status === 'active' && (
                    <Button variant="outline" size="sm" asChild>
                      <a href={`https://${domain.domain}`} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-3 h-3 mr-1" />
                        Visit
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomDomainsManager;
